# LectoAI Environment Configuration
# Copy this file to .env and update the values

# DeepSeek API Configuration
DEEPSEEK_API_KEY=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

# Server Configuration
FLASK_ENV=development
FLASK_DEBUG=True
PORT=5000

# File Upload Configuration
MAX_FILE_SIZE=10485760  # 10MB in bytes
ALLOWED_EXTENSIONS=pdf,docx,pptx,png,jpg,jpeg,bmp,tiff

# OCR Configuration (for Tesseract)
# TESSERACT_CMD=/usr/bin/tesseract  # Uncomment and set path if needed
