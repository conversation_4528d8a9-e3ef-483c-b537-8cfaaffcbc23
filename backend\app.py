# app.py
from flask import Flask, request, jsonify, Response, stream_template
from flask_cors import CORS
import os
import tempfile
import fitz  # PyMuPDF
from pptx import Presentation
from docx import Document
import pytesseract
from PIL import Image
import requests
import json
import time
import re
import hashlib
import threading
from datetime import datetime, timedelta
from collections import defaultdict
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

app = Flask(__name__)
CORS(app)  # Enable CORS for frontend communication

# DeepSeek-R1 API configuration via OpenRouter
OPENROUTER_API_URL = "https://openrouter.ai/api/v1/chat/completions"
DEEPSEEK_API_KEY = "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"

# Performance optimizations
response_cache = {}
conversation_memory = defaultdict(list)
session_lock = threading.Lock()

# Connection pooling for better performance
session = requests.Session()
session.headers.update({
    "Authorization": f"Bearer {DEEPSEEK_API_KEY}",
    "Content-Type": "application/json",
    "HTTP-Referer": "http://localhost:5000",
    "X-Title": "LectoAI Learning Assistant"
})

def extract_text_from_file(file_path, filename):
    """Extract text from various file formats"""
    text = ""

    try:
        if filename.lower().endswith('.pdf'):
            # PDF processing
            doc = fitz.open(file_path)
            for page in doc:
                text += page.get_text() + "\n"
            doc.close()

        elif filename.lower().endswith('.pptx'):
            # PowerPoint processing
            prs = Presentation(file_path)
            for slide in prs.slides:
                for shape in slide.shapes:
                    if hasattr(shape, "text") and shape.text.strip():
                        text += shape.text + "\n"

        elif filename.lower().endswith('.docx'):
            # DOCX processing
            doc = Document(file_path)
            for paragraph in doc.paragraphs:
                if paragraph.text.strip():
                    text += paragraph.text + "\n"

            # Extract text from tables
            for table in doc.tables:
                for row in table.rows:
                    for cell in row.cells:
                        if cell.text.strip():
                            text += cell.text + " "
                    text += "\n"

        elif filename.lower().endswith(('.png', '.jpg', '.jpeg', '.bmp', '.tiff')):
            # Image OCR processing
            img = Image.open(file_path)
            text = pytesseract.image_to_string(img)

    except Exception as e:
        print(f"Error extracting text from {filename}: {str(e)}")
        text = f"Error processing file: {str(e)}"

    return text.strip()

@app.route('/upload', methods=['POST'])
def upload_file():
    """Handle file upload and text extraction"""
    if 'file' not in request.files:
        return jsonify({"error": "No file part"}), 400

    file = request.files['file']
    if file.filename == '':
        return jsonify({"error": "No selected file"}), 400

    # Check file extension
    allowed_extensions = {'.pdf', '.docx', '.pptx', '.png', '.jpg', '.jpeg', '.bmp', '.tiff'}
    file_ext = os.path.splitext(file.filename.lower())[1]

    if file_ext not in allowed_extensions:
        return jsonify({"error": f"Unsupported file type: {file_ext}"}), 400

    try:
        # Save temporary file
        with tempfile.NamedTemporaryFile(delete=False, suffix=file_ext) as temp_file:
            file.save(temp_file.name)
            extracted_text = extract_text_from_file(temp_file.name, file.filename)

        os.unlink(temp_file.name)  # Clean up

        if not extracted_text or extracted_text.startswith("Error processing"):
            return jsonify({"error": "Could not extract text from file"}), 400

        return jsonify({
            "success": True,
            "filename": file.filename,
            "text": extracted_text,
            "text_length": len(extracted_text),
            "preview": extracted_text[:500] + "..." if len(extracted_text) > 500 else extracted_text
        })

    except Exception as e:
        return jsonify({"error": f"Upload failed: {str(e)}"}), 500

def create_conversational_prompt(question, context, session_id):
    """Create enhanced educational prompt with superior response quality"""

    # Get conversation history
    with session_lock:
        conversation_history = conversation_memory[session_id][-10:]  # Last 5 exchanges

    if context:
        # Analyze context to determine content type and complexity
        context_lower = context.lower()

        if any(word in context_lower for word in ['algorithm', 'programming', 'code', 'function', 'variable', 'python', 'java', 'javascript']):
            content_type = "Computer Science & Programming"
            subject_expertise = "programming languages, algorithms, data structures, software development"
        elif any(word in context_lower for word in ['equation', 'formula', 'theorem', 'proof', 'mathematics', 'calculus', 'algebra', 'geometry']):
            content_type = "Mathematics"
            subject_expertise = "mathematical concepts, problem-solving, proofs, calculations"
        elif any(word in context_lower for word in ['experiment', 'hypothesis', 'research', 'study', 'analysis', 'biology', 'chemistry', 'physics']):
            content_type = "Science & Research"
            subject_expertise = "scientific methods, experimental design, data analysis, research methodologies"
        elif any(word in context_lower for word in ['history', 'historical', 'century', 'period', 'era', 'civilization', 'culture']):
            content_type = "History & Social Sciences"
            subject_expertise = "historical analysis, cultural context, chronological understanding"
        elif any(word in context_lower for word in ['business', 'economics', 'finance', 'management', 'marketing']):
            content_type = "Business & Economics"
            subject_expertise = "business principles, economic theory, financial analysis"
        else:
            content_type = "General Academic Studies"
            subject_expertise = "critical thinking, analysis, and comprehensive understanding"

        system_prompt = f"""You are Dr. LectoAI, a world-class educational expert and master teacher specializing in {content_type}. You have decades of experience in {subject_expertise} and are renowned for making complex topics accessible and engaging.

EDUCATIONAL PHILOSOPHY:
- Every response should genuinely help the student learn and understand
- Use the Socratic method: guide discovery rather than just providing answers
- Build knowledge progressively from fundamentals to advanced concepts
- Connect new information to what students already know
- Make learning memorable through stories, analogies, and real-world applications

STUDENT'S LEARNING MATERIAL:
{context[:3000]}{"..." if len(context) > 3000 else ""}

RESPONSE EXCELLENCE STANDARDS:
1. **STRUCTURE**: Always use clear headings (##) and logical organization
2. **DEPTH**: Provide comprehensive explanations that go beyond surface level
3. **CLARITY**: Use simple language first, then introduce technical terms with definitions
4. **EXAMPLES**: Include concrete, relatable examples for every major concept
5. **ENGAGEMENT**: Use analogies, stories, or thought experiments to make content memorable
6. **PROGRESSION**: Start with basics and build to more complex ideas
7. **INTERACTION**: Ask thought-provoking questions to encourage deeper thinking
8. **PRACTICAL**: Show how concepts apply in real-world situations
9. **VISUAL**: Use markdown formatting, lists, and code blocks for clarity
10. **ENCOURAGEMENT**: Be supportive and build student confidence

CONVERSATION CONTEXT:
- Reference our previous discussion when relevant
- Build upon concepts we've already covered
- Acknowledge the student's learning journey and progress
- Adapt complexity based on their demonstrated understanding

RESPONSE FORMAT:
- Start with a brief, engaging hook or connection to their question
- Use ## for main sections and ### for subsections
- Include **bold** for key terms and *italics* for emphasis
- Provide code examples in ```language blocks when relevant
- End with a question or suggestion for further exploration

Remember: Your goal is not just to answer questions, but to inspire genuine understanding and curiosity. Make every response a meaningful learning experience."""

        messages = [{"role": "system", "content": system_prompt}]

        # Add conversation history
        messages.extend(conversation_history)

        # Add current question
        messages.append({"role": "user", "content": question})

    else:
        system_prompt = """You are Dr. LectoAI, a world-class educational expert and master teacher with expertise across all academic disciplines. You are renowned for your ability to make any topic accessible, engaging, and memorable.

EDUCATIONAL PHILOSOPHY:
- Every response should genuinely help the student learn and understand
- Use the Socratic method: guide discovery rather than just providing answers
- Build knowledge progressively from fundamentals to advanced concepts
- Connect new information to what students already know
- Make learning memorable through stories, analogies, and real-world applications

RESPONSE EXCELLENCE STANDARDS:
1. **STRUCTURE**: Always use clear headings (##) and logical organization
2. **DEPTH**: Provide comprehensive explanations that go beyond surface level
3. **CLARITY**: Use simple language first, then introduce technical terms with definitions
4. **EXAMPLES**: Include concrete, relatable examples for every major concept
5. **ENGAGEMENT**: Use analogies, stories, or thought experiments to make content memorable
6. **PROGRESSION**: Start with basics and build to more complex ideas
7. **INTERACTION**: Ask thought-provoking questions to encourage deeper thinking
8. **PRACTICAL**: Show how concepts apply in real-world situations
9. **VISUAL**: Use markdown formatting, lists, and code blocks for clarity
10. **ENCOURAGEMENT**: Be supportive and build student confidence

CONVERSATION CONTEXT:
- Reference our previous discussion when relevant and helpful
- Build upon concepts we've already covered
- Acknowledge the student's learning journey and progress
- Adapt complexity based on their demonstrated understanding

RESPONSE FORMAT:
- Start with a brief, engaging hook or connection to their question
- Use ## for main sections and ### for subsections
- Include **bold** for key terms and *italics* for emphasis
- Provide code examples in ```language blocks when relevant
- End with a question or suggestion for further exploration

Remember: Your goal is not just to answer questions, but to inspire genuine understanding and curiosity. Make every response a meaningful learning experience that builds the student's knowledge and confidence."""

        messages = [{"role": "system", "content": system_prompt}]

        # Add conversation history
        messages.extend(conversation_history)

        # Add current question
        messages.append({"role": "user", "content": question})

    return messages

def create_enhanced_prompt(question, context, context_type="general"):
    """Create an advanced prompt optimized for DeepSeek R1 to generate superior educational responses"""

    if context:
        # Advanced context analysis with more sophisticated categorization
        context_lower = context.lower()
        context_preview = context[:4000]  # Increased context window

        # Enhanced content type detection
        if any(word in context_lower for word in ['algorithm', 'programming', 'code', 'function', 'variable', 'class', 'method', 'loop', 'array', 'data structure', 'software', 'debugging', 'syntax']):
            content_type = "Computer Science & Programming"
            subject_expertise = "software engineering, algorithms, data structures, and programming paradigms"
        elif any(word in context_lower for word in ['equation', 'formula', 'theorem', 'proof', 'mathematics', 'calculus', 'algebra', 'geometry', 'statistics', 'probability', 'derivative', 'integral']):
            content_type = "Mathematics & Statistics"
            subject_expertise = "mathematical analysis, problem-solving, and quantitative reasoning"
        elif any(word in context_lower for word in ['experiment', 'hypothesis', 'research', 'study', 'analysis', 'methodology', 'data', 'results', 'conclusion', 'scientific method']):
            content_type = "Scientific Research & Analysis"
            subject_expertise = "research methodology, experimental design, and scientific analysis"
        elif any(word in context_lower for word in ['history', 'historical', 'century', 'period', 'era', 'civilization', 'culture', 'society', 'political', 'economic']):
            content_type = "History & Social Sciences"
            subject_expertise = "historical analysis, cultural studies, and social dynamics"
        elif any(word in context_lower for word in ['business', 'management', 'marketing', 'finance', 'economics', 'strategy', 'leadership', 'organization']):
            content_type = "Business & Economics"
            subject_expertise = "business strategy, economic principles, and organizational behavior"
        elif any(word in context_lower for word in ['psychology', 'behavior', 'cognitive', 'mental', 'brain', 'learning', 'memory', 'perception']):
            content_type = "Psychology & Cognitive Science"
            subject_expertise = "human behavior, cognitive processes, and psychological principles"
        else:
            content_type = "Interdisciplinary Academic Studies"
            subject_expertise = "cross-disciplinary analysis and comprehensive academic understanding"

        # Advanced system prompt with sophisticated educational framework
        system_prompt = f"""You are Dr. LectoAI, a world-renowned educational expert and master teacher specializing in {content_type}. You have decades of experience in {subject_expertise} and are celebrated for transforming complex concepts into clear, engaging, and memorable learning experiences.

🎓 EDUCATIONAL PHILOSOPHY & APPROACH:
- Apply the Socratic method: Guide students to discover insights through thoughtful questioning
- Use Bloom's Taxonomy: Progress from knowledge → comprehension → application → analysis → synthesis → evaluation
- Employ multiple learning modalities: Visual, auditory, kinesthetic explanations
- Connect new concepts to prior knowledge and real-world applications
- Foster critical thinking and intellectual curiosity

📚 STUDENT'S LEARNING MATERIAL:
{context_preview}{"..." if len(context) > 4000 else ""}

🎯 RESPONSE EXCELLENCE STANDARDS:
1. **Comprehensive Understanding**: Provide deep, nuanced explanations that go beyond surface-level information
2. **Structured Learning**: Use clear hierarchical organization with logical flow
3. **Active Engagement**: Include thought-provoking questions and interactive elements
4. **Practical Application**: Connect theory to real-world examples and use cases
5. **Visual Learning**: Use ASCII diagrams, tables, and structured formatting when helpful
6. **Progressive Complexity**: Start with fundamentals and build to advanced concepts
7. **Memory Aids**: Include mnemonics, analogies, and memorable frameworks
8. **Assessment Integration**: Provide self-check questions and knowledge validation

📝 ADVANCED FORMATTING REQUIREMENTS:
- Use ## for major sections, ### for subsections
- **Bold** for key terms and critical concepts
- `code formatting` for technical terms, formulas, or specific examples
- > Blockquotes for important insights, definitions, or key takeaways
- • Bullet points for lists and step-by-step processes
- Tables for comparisons and organized data
- ASCII diagrams for visual concepts when appropriate
- 🎯 Emojis sparingly for section headers and emphasis

🧠 COGNITIVE ENHANCEMENT TECHNIQUES:
- Begin with a clear overview/roadmap of what will be covered
- Use the "Tell them what you're going to tell them, tell them, then tell them what you told them" structure
- Include transition sentences between major concepts
- End with synthesis and next steps for deeper learning
- Provide multiple perspectives on complex topics

Remember: Your goal is to create an exceptional learning experience that not only answers the question but elevates the student's understanding and inspires continued exploration of the subject."""

        messages = [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": f"Based on the comprehensive lecture material provided above, please provide an expert-level educational response to this question: {question}\n\nPlease ensure your response demonstrates mastery of the subject matter while being accessible and engaging for learning purposes."}
        ]
    else:
        # Enhanced general prompt for questions without specific context
        system_prompt = """You are Dr. LectoAI, a distinguished educational expert with expertise across all academic disciplines. You are renowned for your ability to make any topic accessible, engaging, and memorable through exceptional teaching methods.

🎓 EDUCATIONAL EXCELLENCE STANDARDS:
1. **Deep Understanding**: Provide comprehensive, nuanced explanations
2. **Clear Structure**: Use logical organization with clear progression
3. **Engaging Delivery**: Make learning interesting and memorable
4. **Practical Relevance**: Connect concepts to real-world applications
5. **Critical Thinking**: Encourage analysis and deeper reflection
6. **Multiple Perspectives**: Present different viewpoints when relevant

📝 RESPONSE FORMATTING:
- Use ## for major sections, ### for subsections
- **Bold** for key terms and important concepts
- `code formatting` for technical terms or specific examples
- > Blockquotes for crucial insights and definitions
- • Bullet points for organized lists
- Tables for comparisons and structured data
- 🎯 Strategic emoji use for emphasis and organization

🧠 TEACHING METHODOLOGY:
- Start with clear context and overview
- Build from fundamentals to advanced concepts
- Use analogies and real-world examples
- Include self-assessment opportunities
- Provide actionable next steps for learning

Your mission: Transform every response into an exceptional learning experience that builds knowledge, understanding, and intellectual curiosity."""

        messages = [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": f"Please provide a comprehensive, educational response to this question: {question}\n\nEnsure your response is well-structured, engaging, and demonstrates expert-level knowledge while being accessible for learning purposes."}
        ]

    return messages

@app.route('/ask', methods=['POST'])
def ask_question():
    """Handle user questions using DeepSeek-R1 API via OpenRouter with optimizations"""
    data = request.json
    context = data.get('context', '')
    question = data.get('question', '')

    if not question:
        return jsonify({"error": "No question provided"}), 400

    try:
        # Determine if this is a summary request and use specialized prompt
        question_lower = question.lower()
        if context and any(word in question_lower for word in ['summary', 'summarize', 'overview', 'main points', 'key points']):
            messages = create_summary_prompt(context, question)
        else:
            # Create enhanced prompt for regular questions
            messages = create_enhanced_prompt(question, context)

        # Optimized API call with retry mechanism
        headers = {
            "Authorization": f"Bearer {DEEPSEEK_API_KEY}",
            "Content-Type": "application/json",
            "HTTP-Referer": "http://localhost:5000",
            "X-Title": "LectoAI Learning Assistant"
        }

        # Optimized parameters for DeepSeek R1 Free Tier
        payload = {
            "model": "deepseek/deepseek-r1:free",  # Correct free tier model name
            "messages": messages,
            "temperature": 0.7,  # Balanced for educational creativity
            "max_tokens": 2048,  # Conservative for free tier limits
            "stream": False,
            "top_p": 0.9,  # Standard sampling
        }

        # Retry mechanism
        max_retries = 2
        for attempt in range(max_retries + 1):
            try:
                response = requests.post(
                    OPENROUTER_API_URL,
                    json=payload,
                    headers=headers,
                    timeout=45  # Increased timeout
                )

                if response.status_code == 200:
                    break
                elif response.status_code == 429:  # Rate limit
                    if attempt < max_retries:
                        time.sleep(2 ** attempt)  # Exponential backoff
                        continue

            except requests.exceptions.Timeout:
                if attempt < max_retries:
                    continue
                raise

        if response.status_code != 200:
            error_msg = f"API Error {response.status_code}: {response.text}"
            print(error_msg)

            # Handle specific DeepSeek free tier errors
            if response.status_code == 429:
                return jsonify({
                    "error": "DeepSeek free tier rate limit reached. Please wait a moment and try again.",
                    "retry": True,
                    "fallback_available": True
                }), 429
            elif response.status_code == 402:
                return jsonify({
                    "error": "DeepSeek free tier daily limit reached. Using intelligent fallback response.",
                    "retry": False,
                    "fallback_available": True
                }), 402
            else:
                return jsonify({"error": "AI service temporarily unavailable", "retry": True}), 503

        response_data = response.json()

        # Extract and enhance AI response
        if 'choices' in response_data and len(response_data['choices']) > 0:
            raw_response = response_data['choices'][0]['message']['content']
            enhanced_response = enhance_ai_response(raw_response, question, context)

            return jsonify({
                "success": True,
                "answer": enhanced_response,
                "model": "deepseek-r1-free",
                "formatted": True,  # Indicates response is markdown formatted
                "enhanced": True  # Indicates response has been post-processed for quality
            })
        else:
            return jsonify({"error": "Invalid response from AI service"}), 500

    except requests.exceptions.Timeout:
        return jsonify({"error": "Request timeout - please try again", "retry": True}), 408
    except requests.exceptions.RequestException as e:
        error_str = str(e)
        if "429" in error_str or "rate limit" in error_str.lower():
            # Free tier rate limit - provide fallback
            fallback_response = generate_intelligent_fallback(question, context)
            return jsonify({
                "success": True,
                "answer": fallback_response,
                "model": "fallback",
                "formatted": True,
                "fallback": True,
                "message": "Using intelligent fallback due to rate limits"
            })
        return jsonify({"error": f"Network error: {error_str}", "retry": True}), 503
    except Exception as e:
        print(f"Error in ask_question: {str(e)}")
        # Provide fallback for any other errors
        fallback_response = generate_intelligent_fallback(question, context)
        return jsonify({
            "success": True,
            "answer": fallback_response,
            "model": "fallback",
            "formatted": True,
            "fallback": True,
            "message": "Using intelligent fallback due to service issues"
        })

@app.route('/ask-stream', methods=['POST'])
def ask_question_stream():
    """Handle user questions with streaming responses for ChatGPT-like experience"""
    data = request.json
    context = data.get('context', '')
    question = data.get('question', '')
    session_id = data.get('session_id', 'default')

    if not question:
        return jsonify({"error": "No question provided"}), 400

    def generate_stream():
        try:
            # Add to conversation memory
            with session_lock:
                conversation_memory[session_id].append({"role": "user", "content": question})
                # Keep only last 10 exchanges to manage memory
                if len(conversation_memory[session_id]) > 20:
                    conversation_memory[session_id] = conversation_memory[session_id][-20:]

            # Check cache first for faster responses
            cache_key = hashlib.md5(f"{question}:{context[:500]}".encode()).hexdigest()
            if cache_key in response_cache:
                cached_response = response_cache[cache_key]
                # Stream cached response word by word for consistency
                words = cached_response.split()
                for word in words:
                    yield f"data: {json.dumps({'token': word + ' ', 'done': False})}\n\n"
                    time.sleep(0.05)  # Simulate streaming
                yield f"data: {json.dumps({'token': '', 'done': True, 'cached': True})}\n\n"
                return

            # Create enhanced prompt with conversation memory
            messages = create_conversational_prompt(question, context, session_id)

            # Optimized parameters for DeepSeek R1 Free Tier streaming
            payload = {
                "model": "deepseek/deepseek-r1:free",  # Correct free tier model name
                "messages": messages,
                "temperature": 0.7,  # Balanced for educational responses
                "max_tokens": 2048,  # Conservative for free tier
                "stream": True,  # Enable streaming for better UX
                "top_p": 0.9,  # Standard sampling
            }

            # Make streaming request
            response = session.post(OPENROUTER_API_URL, json=payload, stream=True, timeout=60)

            if response.status_code == 402:
                # Free tier limit reached - provide helpful fallback
                fallback_response = generate_intelligent_fallback(question, context)
                words = fallback_response.split()
                for word in words:
                    yield f"data: {json.dumps({'token': word + ' ', 'done': False})}\n\n"
                    time.sleep(0.08)  # Simulate streaming
                yield f"data: {json.dumps({'token': '', 'done': True, 'fallback': True})}\n\n"
                return
            elif response.status_code != 200:
                yield f"data: {json.dumps({'error': f'API Error {response.status_code}', 'done': True})}\n\n"
                return

            full_response = ""

            # Process streaming response
            for line in response.iter_lines():
                if line:
                    line = line.decode('utf-8')
                    if line.startswith('data: '):
                        try:
                            data_str = line[6:]  # Remove 'data: ' prefix
                            if data_str.strip() == '[DONE]':
                                break

                            chunk_data = json.loads(data_str)
                            if 'choices' in chunk_data and len(chunk_data['choices']) > 0:
                                delta = chunk_data['choices'][0].get('delta', {})
                                if 'content' in delta:
                                    token = delta['content']
                                    full_response += token
                                    yield f"data: {json.dumps({'token': token, 'done': False})}\n\n"
                        except json.JSONDecodeError:
                            continue

            # Cache the response for future use
            if full_response:
                response_cache[cache_key] = full_response
                # Add to conversation memory
                with session_lock:
                    conversation_memory[session_id].append({"role": "assistant", "content": full_response})

            yield f"data: {json.dumps({'token': '', 'done': True})}\n\n"

        except Exception as e:
            yield f"data: {json.dumps({'error': str(e), 'done': True})}\n\n"

    return Response(generate_stream(), mimetype='text/event-stream',
                   headers={'Cache-Control': 'no-cache', 'Connection': 'keep-alive'})

@app.route('/conversation/<session_id>', methods=['GET'])
def get_conversation(session_id):
    """Get conversation history for a session"""
    with session_lock:
        history = conversation_memory.get(session_id, [])
    return jsonify({
        "success": True,
        "conversation": history,
        "message_count": len(history)
    })

@app.route('/conversation/<session_id>', methods=['DELETE'])
def clear_conversation(session_id):
    """Clear conversation history for a session"""
    with session_lock:
        if session_id in conversation_memory:
            del conversation_memory[session_id]
    return jsonify({
        "success": True,
        "message": "Conversation cleared"
    })

@app.route('/cache/clear', methods=['POST'])
def clear_cache():
    """Clear response cache"""
    global response_cache
    response_cache = {}
    return jsonify({
        "success": True,
        "message": "Cache cleared"
    })

@app.route('/generate-questions', methods=['POST'])
def generate_questions():
    """Generate dynamic questions based on document content"""
    data = request.json
    context = data.get('context', '')

    if not context:
        return jsonify({"error": "No content provided"}), 400

    try:
        # Analyze content and generate relevant questions
        content_preview = context[:1500]  # Use first 1500 chars for analysis

        system_prompt = """You are an educational content analyzer. Based on the provided text, generate 5-7 specific, thoughtful questions that would help a student understand and engage with this material.

REQUIREMENTS:
1. Questions should be directly related to the content
2. Mix different types: factual, analytical, and conceptual
3. Make questions educational and thought-provoking
4. Avoid generic questions - be specific to the content
5. Return ONLY a JSON array of questions, no other text

Example format: ["What is the main concept discussed?", "How does X relate to Y?", "What are the key differences between A and B?"]"""

        messages = [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": f"Generate specific questions for this content:\n\n{content_preview}"}
        ]

        headers = {
            "Authorization": f"Bearer {DEEPSEEK_API_KEY}",
            "Content-Type": "application/json",
            "HTTP-Referer": "http://localhost:5000",
            "X-Title": "LectoAI Learning Assistant"
        }

        payload = {
            "model": "deepseek/deepseek-r1:free",  # Correct free tier model name
            "messages": messages,
            "temperature": 0.8,
            "max_tokens": 500,
            "stream": False
        }

        response = requests.post(OPENROUTER_API_URL, json=payload, headers=headers, timeout=30)

        if response.status_code == 200:
            response_data = response.json()
            if 'choices' in response_data and len(response_data['choices']) > 0:
                ai_response = response_data['choices'][0]['message']['content']

                # Try to parse JSON response
                try:
                    # Extract JSON array from response
                    import json
                    # Look for JSON array pattern
                    json_match = re.search(r'\[.*?\]', ai_response, re.DOTALL)
                    if json_match:
                        questions = json.loads(json_match.group())
                    else:
                        # Fallback: split by lines and clean
                        lines = ai_response.strip().split('\n')
                        questions = [line.strip(' "•-1234567890.') for line in lines if line.strip() and '?' in line][:6]

                    return jsonify({
                        "success": True,
                        "questions": questions[:6]  # Limit to 6 questions
                    })

                except (json.JSONDecodeError, AttributeError):
                    # Fallback to manual parsing
                    lines = ai_response.strip().split('\n')
                    questions = []
                    for line in lines:
                        line = line.strip(' "•-1234567890.')
                        if line and '?' in line and len(line) > 10:
                            questions.append(line)

                    return jsonify({
                        "success": True,
                        "questions": questions[:6]
                    })

        # Fallback questions based on content analysis
        fallback_questions = generate_fallback_questions(context)
        return jsonify({
            "success": True,
            "questions": fallback_questions,
            "fallback": True
        })

    except Exception as e:
        print(f"Error generating questions: {str(e)}")
        fallback_questions = generate_fallback_questions(context)
        return jsonify({
            "success": True,
            "questions": fallback_questions,
            "fallback": True
        })

def generate_fallback_questions(context):
    """Generate fallback questions based on content analysis"""
    context_lower = context.lower()
    questions = []

    # Analyze content type and generate relevant questions
    if any(word in context_lower for word in ['algorithm', 'programming', 'code', 'function']):
        questions = [
            "What are the main algorithms or programming concepts discussed?",
            "How do these programming techniques work?",
            "What are the time and space complexities involved?",
            "Can you provide examples of how to implement these concepts?",
            "What are the practical applications of these programming methods?"
        ]
    elif any(word in context_lower for word in ['equation', 'formula', 'theorem', 'proof']):
        questions = [
            "What are the key mathematical concepts presented?",
            "How are these formulas derived?",
            "What are the practical applications of these equations?",
            "Can you explain the proof or reasoning behind these theorems?",
            "What are some examples of solving problems using these methods?"
        ]
    elif any(word in context_lower for word in ['experiment', 'hypothesis', 'research', 'study']):
        questions = [
            "What is the main hypothesis or research question?",
            "What methodology was used in this study?",
            "What were the key findings or results?",
            "What are the implications of these research findings?",
            "How does this research relate to other studies in the field?"
        ]
    else:
        # Generic educational questions
        questions = [
            "What are the main concepts or topics covered in this material?",
            "Can you explain the key points in more detail?",
            "What are some real-world applications of these concepts?",
            "How do these ideas relate to other topics in the field?",
            "What are the most important takeaways from this content?"
        ]

    return questions[:5]

def enhance_ai_response(response_text, question, context=""):
    """Post-process AI responses to ensure educational quality and formatting"""
    if not response_text or len(response_text.strip()) < 50:
        return generate_intelligent_fallback(question, context)

    enhanced_response = response_text.strip()

    # Detect poor quality responses
    poor_quality_indicators = [
        len(enhanced_response) < 100,  # Too short
        enhanced_response.count('.') < 3,  # Too few sentences
        'I cannot' in enhanced_response and len(enhanced_response) < 200,  # Refusal without explanation
        enhanced_response.lower().count('sorry') > 2,  # Too many apologies
        not any(char.isalpha() for char in enhanced_response),  # No actual content
    ]

    if any(poor_quality_indicators):
        print(f"Poor quality response detected for question: {question[:100]}...")
        return generate_intelligent_fallback(question, context)

    # Enhance formatting if needed
    if not any(marker in enhanced_response for marker in ['##', '###', '**', '*']):
        # Add basic educational structure
        lines = enhanced_response.split('\n')
        formatted_lines = []
        current_section = []

        for line in lines:
            line = line.strip()
            if not line:
                if current_section:
                    formatted_lines.append('\n'.join(current_section))
                    current_section = []
                continue

            # Detect potential headers (questions or statements ending with colons)
            if (line.endswith(':') or line.endswith('?')) and len(line) < 100:
                if current_section:
                    formatted_lines.append('\n'.join(current_section))
                    current_section = []
                formatted_lines.append(f"## {line}")
            # Detect key points (longer sentences)
            elif len(line) > 80 and line.endswith('.') and not line.startswith(('•', '-', '1.', '2.')):
                current_section.append(f"**{line}**")
            else:
                current_section.append(line)

        if current_section:
            formatted_lines.append('\n'.join(current_section))

        enhanced_response = '\n\n'.join(formatted_lines)

    # Ensure educational value
    if context and len(enhanced_response) > 200:
        # Add context reference if missing
        if 'document' not in enhanced_response.lower() and 'material' not in enhanced_response.lower():
            enhanced_response = f"Based on the provided learning material:\n\n{enhanced_response}"

    return enhanced_response

def create_summary_prompt(context, question=""):
    """Create specialized prompt for document summarization with enhanced quality"""

    # Analyze document type and content
    context_lower = context.lower()
    doc_length = len(context)

    # Determine document characteristics
    if any(word in context_lower for word in ['algorithm', 'programming', 'code', 'function']):
        doc_type = "Technical/Programming"
        focus_areas = "algorithms, code examples, programming concepts, implementation details"
    elif any(word in context_lower for word in ['research', 'study', 'experiment', 'hypothesis']):
        doc_type = "Research/Academic"
        focus_areas = "research objectives, methodology, findings, conclusions, implications"
    elif any(word in context_lower for word in ['business', 'management', 'strategy', 'market']):
        doc_type = "Business/Management"
        focus_areas = "business strategies, market analysis, management principles, key recommendations"
    else:
        doc_type = "General Academic"
        focus_areas = "main concepts, key principles, important details, practical applications"

    system_prompt = f"""You are Dr. LectoAI, a master educator and expert document analyst specializing in creating comprehensive, insightful summaries. You excel at distilling complex information into clear, structured, and educationally valuable summaries.

📄 DOCUMENT ANALYSIS:
Document Type: {doc_type}
Content Length: {doc_length} characters
Focus Areas: {focus_areas}

DOCUMENT CONTENT:
{context[:5000]}{"..." if len(context) > 5000 else ""}

🎯 SUMMARY EXCELLENCE STANDARDS:

**1. COMPREHENSIVE COVERAGE**
- Capture all major themes, concepts, and key points
- Include important details, examples, and supporting information
- Maintain the logical flow and structure of the original content

**2. EDUCATIONAL VALUE**
- Explain complex concepts in accessible language
- Provide context and background where needed
- Highlight connections between different ideas

**3. STRUCTURED PRESENTATION**
- Use clear headings and subheadings (## ###)
- Organize information logically and hierarchically
- Include bullet points and numbered lists for clarity

**4. DEPTH AND INSIGHT**
- Go beyond surface-level information
- Identify underlying principles and patterns
- Explain the significance and implications of key points

**5. PRACTICAL UTILITY**
- Make the summary useful for study and reference
- Include actionable insights where relevant
- Suggest areas for further exploration

📝 REQUIRED SUMMARY FORMAT:

## 📚 Document Overview
[Brief introduction to the document's purpose and scope]

## 🎯 Key Concepts & Main Points
[Detailed breakdown of primary topics with explanations]

## 💡 Important Details & Examples
[Significant supporting information, data, examples]

## 🔗 Connections & Relationships
[How different concepts relate to each other]

## 🚀 Practical Applications & Implications
[Real-world relevance and applications]

## 📖 Study Focus Areas
[What students should pay special attention to]

Remember: Create a summary that serves as both a comprehensive overview and a valuable study resource. Make it so thorough and well-organized that someone could understand the essential content without reading the original document."""

    if question and "summary" in question.lower():
        user_prompt = f"Please provide a comprehensive, educational summary of the document content above. {question}"
    else:
        user_prompt = "Please provide a comprehensive, educational summary of the document content above, following the structured format specified."

    return [
        {"role": "system", "content": system_prompt},
        {"role": "user", "content": user_prompt}
    ]

def generate_intelligent_fallback(question, context=""):
    """Generate high-quality educational fallback responses when API is unavailable"""
    question_lower = question.lower()

    # Enhanced educational responses based on common topics and context analysis
    if context:
        context_lower = context.lower()
        # Analyze context for better fallback responses
        if any(word in context_lower for word in ['summary', 'summarize', 'overview', 'main points']):
            return f"""## 📚 Document Summary Analysis

I understand you're looking for a summary of the uploaded material. While I'm currently experiencing connectivity issues with the AI service, I can provide you with a structured approach to analyzing your document:

### 🎯 Key Elements to Focus On:

**1. Main Topics & Themes**
- Identify the primary subject matter
- Look for recurring concepts and terminology
- Note the overall scope and purpose

**2. Critical Information**
- Key facts, figures, and data points
- Important definitions and explanations
- Core arguments or hypotheses presented

**3. Structure & Organization**
- How the content is organized (chronologically, thematically, etc.)
- Major sections and their relationships
- Logical flow of information

### 💡 Suggested Questions to Explore:
- What are the 3-5 most important concepts covered?
- How do the different sections relate to each other?
- What practical applications or examples are provided?
- What questions does this material raise for further study?

**🔄 Please try your request again in a moment, as the AI service should be available shortly for a comprehensive analysis of your specific content.**"""

    # Topic-specific intelligent fallbacks
    if any(word in question_lower for word in ['neural network', 'machine learning', 'ai', 'artificial intelligence']):
        return """## 🧠 Understanding Neural Networks and Machine Learning

Great question! Let me break down these fascinating concepts in a way that builds your understanding step by step.

### What Are Neural Networks?
**Neural networks** are computational systems inspired by how our brains process information. Just like your brain has billions of interconnected neurons, artificial neural networks have layers of interconnected nodes that work together to solve problems.

### 🏗️ The Architecture: How They're Built

#### **1. Input Layer** - The Gateway
- Receives raw data (like pixels in a photo or words in a sentence)
- Think of it as your eyes seeing something for the first time

#### **2. Hidden Layers** - The Processing Powerhouse
- Transform and analyze the data through mathematical operations
- Each layer learns increasingly complex patterns
- Like your brain recognizing shapes, then objects, then meaning

#### **3. Output Layer** - The Decision Maker
- Produces the final answer or prediction
- Could be "This is a cat" or "Buy this stock"

### 🔧 Key Components That Make It Work

**Neurons (Nodes)**: The basic processing units
- Receive multiple inputs
- Apply mathematical functions
- Send output to the next layer

**Weights**: The "importance" values
- Determine how much influence each connection has
- Adjusted during learning to improve accuracy
- Like strengthening neural pathways in your brain

**Training**: The learning process
- Show the network thousands of examples
- It adjusts weights to minimize errors
- Similar to how you learned to recognize faces through repetition

### 🌟 Real-World Applications You Use Daily

1. **Your Phone's Camera**: Recognizes faces for photo tagging
2. **Netflix/Spotify**: Recommends content based on your preferences
3. **Google Translate**: Converts languages in real-time
4. **Voice Assistants**: Understand and respond to speech
5. **Medical Imaging**: Helps doctors detect diseases earlier

### 💡 A Powerful Analogy: Learning to Drive

Imagine learning to drive:
- **Input**: Road conditions, traffic, signs (like input layer)
- **Processing**: Your brain evaluates dangers, distances, speeds (like hidden layers)
- **Output**: Steering, braking, accelerating decisions (like output layer)

The more you practice, the better your "weights" become at making good driving decisions!

### 🤔 Think About This:
*How might the way you learned to recognize your best friend's voice be similar to how a neural network learns to recognize speech patterns?*

### 🚀 Want to Dive Deeper?
Consider exploring:
- How backpropagation works (the learning algorithm)
- Different types of neural networks (CNN, RNN, Transformers)
- The mathematics behind activation functions

*💡 Pro Tip: Upload your course materials for personalized explanations tailored to your specific curriculum and learning level!*"""

    elif any(word in question_lower for word in ['algorithm', 'programming', 'code', 'function']):
        return """## 💻 Mastering Programming and Algorithms

Excellent! You're diving into one of the most powerful and creative fields in technology. Let me guide you through these concepts with clarity and practical insight.

### 🎯 What Exactly Is an Algorithm?

An **algorithm** is like a recipe for solving problems. Just as a cooking recipe gives you step-by-step instructions to make a dish, an algorithm provides precise steps to solve computational problems.

#### **The Essential Qualities of Great Algorithms:**

1. **🎯 Precision**: Every step is crystal clear and unambiguous
2. **⏰ Finite**: It must eventually reach a conclusion
3. **✅ Effective**: Each step must be actually doable
4. **🔄 Input/Output**: Takes something in, produces something useful

### 🏗️ Algorithm Categories: Your Problem-Solving Toolkit

#### **1. Sorting Algorithms** - Organizing Chaos
*"How do we arrange things in order?"*

- **Bubble Sort**: Simple but slow (like organizing cards one swap at a time)
- **Quick Sort**: Divide and conquer approach (like organizing by splitting into groups)
- **Merge Sort**: Stable and predictable (like merging two sorted lists)

#### **2. Search Algorithms** - Finding Needles in Haystacks
*"How do we find what we're looking for efficiently?"*

- **Linear Search**: Check every item one by one
- **Binary Search**: Smart elimination (like guessing a number by halving the range)

#### **3. Graph Algorithms** - Understanding Connections
*"How do we navigate networks and relationships?"*

- **Shortest Path**: Finding the best route (like GPS navigation)
- **Network Analysis**: Understanding how things connect

### 🎨 Programming: Turning Ideas Into Reality

Programming is the art of expressing algorithms in a language computers understand. It's creative problem-solving with logical precision.

#### **🌟 Best Practices for Clean, Effective Code:**

1. **📝 Readable Names**: `calculateStudentGrade()` not `calc()`
2. **💬 Clear Comments**: Explain the "why," not just the "what"
3. **🧪 Test Everything**: Assume nothing, verify everything
4. **⚡ Think Efficiency**: Consider how your code performs with large data
5. **🔧 Refactor Often**: Improve code structure as you learn

### 💡 Let's See It In Action: Linear Search

Here's a practical example that demonstrates clean, educational code:

**Python Implementation:**
- Clear function name that explains purpose
- Detailed docstring for documentation
- Meaningful variable names
- Helpful print statements for learning
- Example usage to show how it works

**Key Learning Points:**
- The algorithm checks each item sequentially
- It returns immediately when found (efficiency)
- It handles the "not found" case gracefully
- The code is self-documenting and easy to understand

### 🚀 The Big Picture: Why This Matters

**Algorithms are everywhere:**
- 🔍 Google's search uses sophisticated ranking algorithms
- 📱 Your phone's autocorrect uses string matching algorithms
- 🎵 Spotify's recommendations use collaborative filtering algorithms
- 🚗 GPS navigation uses shortest-path algorithms

### 🤔 Challenge Yourself:
*Can you think of a real-world problem you face daily that could be solved more efficiently with a good algorithm? How would you break it down into steps?*

### 🎯 Next Steps in Your Journey:
1. **Practice**: Start with simple problems and build complexity
2. **Analyze**: Study how existing algorithms work
3. **Optimize**: Learn to measure and improve efficiency
4. **Apply**: Use algorithms to solve real problems you care about

### 📚 Pro Learning Tips:
- **Visualize**: Draw out how algorithms work step-by-step
- **Code Along**: Don't just read—implement and experiment
- **Debug Mindfully**: Errors are learning opportunities, not failures
- **Connect Concepts**: See how different algorithms relate to each other

*🎓 Ready for personalized guidance? Upload your programming assignments or course materials, and I'll provide specific help tailored to your curriculum and current skill level!*"""

    elif any(word in question_lower for word in ['math', 'equation', 'formula', 'calculus', 'algebra']):
        return """## 📐 Mastering Mathematics: Your Guide to Mathematical Thinking

Mathematics is the language of patterns, logic, and problem-solving. Let me help you develop both the skills and confidence to excel in mathematical thinking.

### 🎯 The Mathematical Problem-Solving Framework

Think of this as your reliable roadmap for tackling any mathematical challenge:

#### **1. 🔍 Understand** - Decode the Problem
- Read the problem multiple times, slowly
- Identify what's being asked (the goal)
- Highlight key information and given values
- *Ask yourself: "What is this problem really about?"*

#### **2. 📋 Plan** - Strategy Selection
- What do you know? (Given information)
- What do you need to find? (Unknown variables)
- What tools can help? (Formulas, theorems, methods)
- *Think: "What's my path from here to the answer?"*

#### **3. ⚡ Solve** - Execute Your Strategy
- Apply your chosen method step-by-step
- Show all work clearly (helps catch errors)
- Use proper mathematical notation
- *Remember: "Neat work leads to correct answers"*

#### **4. ✅ Check** - Verify and Reflect
- Does your answer make sense in context?
- Can you solve it a different way to confirm?
- Are the units correct?
- *Ask: "If I were explaining this to a friend, would it make sense?"*

### 🧠 Core Mathematical Concepts Explained

#### **Variables** - The Mathematical Unknowns
Think of variables as empty boxes waiting to be filled with numbers. When we write `x = 5`, we're saying "the box labeled 'x' contains the number 5."

#### **Functions** - Mathematical Machines
A function is like a machine: you put something in (input), it processes it according to a rule, and gives you something back (output).
- Input: `x = 3`
- Rule: `f(x) = 2x + 1`
- Output: `f(3) = 7`

#### **Equations** - Mathematical Balance Scales
An equation says "these two things are equal." Like a balance scale, whatever you do to one side, you must do to the other to keep it balanced.

#### **Derivatives** (Calculus) - Measuring Change
Derivatives tell us how fast something is changing at any given moment. Like the speedometer in your car showing your speed right now, not your average speed.

#### **Integrals** (Calculus) - Accumulating Change
Integrals add up all the little changes to find the total. Like calculating the total distance traveled by adding up all the little distances covered each second.

### 🎓 Proven Study Strategies That Work

#### **1. 🔄 Active Practice**
- Don't just read examples—work through them yourself
- Start with easier problems, gradually increase difficulty
- Practice a little bit every day rather than cramming

#### **2. 🧩 Concept Connection**
- Understand the "why" behind formulas, not just the "how"
- Connect new concepts to things you already know
- Create concept maps showing how ideas relate

#### **3. 🗣️ Teach to Learn**
- Explain concepts out loud (even to yourself)
- If you can't explain it simply, you don't understand it yet
- Study with friends and take turns teaching each other

#### **4. 🔍 Error Analysis**
- When you make mistakes, figure out exactly where and why
- Keep a "mistake journal" to avoid repeating errors
- Remember: mistakes are learning opportunities, not failures

### 📚 Essential Formulas with Context

#### **Quadratic Formula**: `x = (-b ± √(b²-4ac)) / 2a`
*When to use*: Solving equations like `ax² + bx + c = 0`
*Real-world example*: Finding when a thrown ball hits the ground

#### **Distance Formula**: `d = √((x₂-x₁)² + (y₂-y₁)²)`
*When to use*: Finding distance between two points
*Real-world example*: GPS calculating distance between locations

#### **Area of Circle**: `A = πr²`
*When to use*: Finding area inside a circular boundary
*Real-world example*: Calculating pizza size or garden space

### 🌟 Building Mathematical Confidence

**Remember**: Every mathematician started as a beginner. The key is persistence, not perfection.

- **Celebrate small wins**: Solved a problem? That's progress!
- **Embrace the struggle**: Difficulty means your brain is growing
- **Ask questions**: There are no stupid questions in mathematics
- **Use resources**: Textbooks, online tools, study groups, tutors

### 🤔 Reflection Question:
*Think about a time when you successfully learned something difficult. What strategies did you use? How can you apply those same strategies to mathematics?*

### 🚀 Your Next Steps:
1. **Identify your current challenge**: What specific math topic needs attention?
2. **Gather resources**: Textbook, notes, practice problems
3. **Create a study plan**: Small, consistent sessions work better than marathon cramming
4. **Find a study buddy**: Learning together makes it more enjoyable

*📖 Want personalized help? Upload your math homework, textbook problems, or course materials, and I'll provide step-by-step solutions and explanations tailored to your specific curriculum and learning style!*"""

    else:
        # Enhanced general educational response
        return f"""## 🎓 Personalized Learning Guidance

Thank you for your thoughtful question: *"{question}"*

I'm here to help you learn effectively and build genuine understanding. Let me provide you with a comprehensive approach to tackle this topic.

### 🧠 Strategic Learning Framework

#### **1. 🔍 Deconstruction** - Break It Down
- Identify the core components of your question
- Separate what you know from what you need to learn
- Look for patterns or connections to familiar concepts
- *Think: "What are the building blocks of this topic?"*

#### **2. 🎯 Active Engagement** - Learn by Doing
- Don't just read—interact with the material
- Create examples, solve problems, ask "what if" questions
- Test your understanding by explaining it in your own words
- *Remember: "Understanding comes through practice, not just reading"*

#### **3. 🌐 Connection Building** - Link New to Known
- How does this relate to what you already understand?
- Can you find analogies from everyday life?
- What broader principles or patterns does this illustrate?
- *Ask yourself: "Where have I seen something similar before?"*

#### **4. 🔄 Reinforcement** - Make It Stick
- Review key concepts regularly (spaced repetition)
- Apply knowledge in different contexts
- Teach the concept to someone else (or explain it aloud)
- *Goal: "Move from recognition to true understanding"*

### 🛠️ Proven Study Techniques

#### **📝 The Feynman Technique**
1. Choose your topic
2. Explain it in simple terms (as if teaching a child)
3. Identify gaps in your explanation
4. Go back and fill those gaps
5. Repeat until you can explain it clearly

#### **🎨 Visual Learning**
- Create mind maps or concept diagrams
- Use colors and symbols to organize information
- Draw out processes or relationships
- Make abstract concepts concrete through visualization

#### **🤝 Collaborative Learning**
- Form study groups with classmates
- Take turns explaining concepts to each other
- Discuss different approaches to problems
- Learn from others' perspectives and methods

### 🎯 Customized Assistance Available

{f"I notice you're working with material about {context[:100]}..." if context else "To provide you with the most helpful, personalized assistance,"}

**Upload your course materials** and I can offer:

#### **📚 Curriculum-Specific Help**
- Explanations aligned with your textbook and course structure
- Examples that match your professor's teaching style
- Practice problems at your current skill level

#### **🔧 Problem-Solving Support**
- Step-by-step solutions with detailed explanations
- Multiple approaches to the same problem
- Common mistake identification and prevention

#### **🎨 Learning Style Adaptation**
- Visual explanations for visual learners
- Practical examples for hands-on learners
- Theoretical frameworks for analytical thinkers

#### **📈 Progress Tracking**
- Identify your strengths and areas for improvement
- Suggest next steps in your learning journey
- Provide increasingly challenging practice opportunities

### 🌟 Building Confidence and Competence

**Remember**: Learning is a process, not a destination. Every expert was once a beginner.

- **Embrace the challenge**: Difficulty means your brain is growing
- **Celebrate progress**: Small steps lead to big achievements
- **Stay curious**: Questions are the engine of learning
- **Be patient**: Deep understanding takes time to develop

### 🤔 Reflection Prompts:
- *What specific aspect of this topic interests you most?*
- *How might understanding this help you in your future goals?*
- *What learning approach has worked best for you in the past?*

### 🚀 Your Next Action Steps:

1. **📤 Upload Materials**: Share your textbooks, lecture notes, or assignments
2. **🎯 Ask Specific Questions**: The more specific, the better I can help
3. **🔄 Practice Regularly**: Consistent effort beats sporadic cramming
4. **🤝 Seek Support**: Don't hesitate to ask for clarification or additional examples

*💡 Pro Tip: The best learning happens when you're actively engaged and asking questions. I'm here to support your learning journey every step of the way!*

---
*🔧 This response is generated in enhanced offline mode. For AI-powered, personalized assistance with detailed explanations and step-by-step solutions, please upload your course materials.*"""

@app.route('/health', methods=['GET'])
def health_check():
    """Health check endpoint"""
    return jsonify({
        "status": "healthy",
        "service": "LectoAI Backend",
        "version": "2.0.0",
        "model": "deepseek/deepseek-r1:free"
    })

@app.route('/test-ai', methods=['POST'])
def test_ai():
    """Test endpoint to verify DeepSeek R1 free tier is working"""
    try:
        test_messages = [
            {"role": "system", "content": "You are a helpful AI assistant. Respond briefly and clearly."},
            {"role": "user", "content": "Say 'Hello! DeepSeek R1 free tier is working correctly.' and nothing else."}
        ]

        payload = {
            "model": "deepseek/deepseek-r1:free",
            "messages": test_messages,
            "temperature": 0.7,
            "max_tokens": 50,
            "stream": False
        }

        headers = {
            "Authorization": f"Bearer {DEEPSEEK_API_KEY}",
            "Content-Type": "application/json",
            "HTTP-Referer": "http://localhost:5000",
            "X-Title": "LectoAI Test"
        }

        response = requests.post(OPENROUTER_API_URL, json=payload, headers=headers, timeout=30)

        if response.status_code == 200:
            response_data = response.json()
            if 'choices' in response_data and len(response_data['choices']) > 0:
                ai_response = response_data['choices'][0]['message']['content']
                return jsonify({
                    "success": True,
                    "message": "DeepSeek R1 free tier is working!",
                    "response": ai_response,
                    "status_code": response.status_code
                })

        return jsonify({
            "success": False,
            "message": f"API Error {response.status_code}",
            "error": response.text[:200]
        }), response.status_code

    except Exception as e:
        return jsonify({
            "success": False,
            "message": "Test failed",
            "error": str(e)
        }), 500

@app.route('/', methods=['GET'])
def home():
    """Home endpoint"""
    return jsonify({
        "message": "LectoAI Backend API",
        "endpoints": {
            "upload": "/upload (POST)",
            "ask": "/ask (POST)",
            "health": "/health (GET)"
        }
    })

if __name__ == '__main__':
    print("🚀 Starting LectoAI Backend Server...")
    print("📚 File upload endpoint: http://localhost:5000/upload")
    print("🤖 AI chat endpoint: http://localhost:5000/ask")
    print("❤️ Health check: http://localhost:5000/health")
    app.run(host='0.0.0.0', port=5000, debug=True)