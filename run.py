#!/usr/bin/env python3
"""
LectoAI - AI Learning Assistant
Simple script to run the application
"""

import os
import sys
import subprocess
import webbrowser
import time
from pathlib import Path

def check_dependencies():
    """Check if required dependencies are installed"""
    try:
        import flask
        import flask_cors
        import fitz
        import pptx
        import docx
        import pytesseract
        import PIL
        import requests
        import dotenv
        print("✅ All dependencies are installed!")
        return True
    except ImportError as e:
        print(f"❌ Missing dependency: {e}")
        print("\n📦 Installing dependencies...")
        try:
            subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
            print("✅ Dependencies installed successfully!")
            return True
        except subprocess.CalledProcessError:
            print("❌ Failed to install dependencies. Please run: pip install -r requirements.txt")
            return False

def start_backend():
    """Start the Flask backend server"""
    backend_path = Path("backend/app.py")
    if not backend_path.exists():
        print("❌ Backend file not found!")
        return None
    
    print("🚀 Starting backend server...")
    return subprocess.Popen([sys.executable, str(backend_path)], cwd=".")

def open_frontend():
    """Open the frontend in the default browser"""
    frontend_path = Path("frontend/index.html").absolute()
    if not frontend_path.exists():
        print("❌ Frontend file not found!")
        return False
    
    print("🌐 Opening frontend in browser...")
    webbrowser.open(f"file://{frontend_path}")
    return True

def main():
    """Main function to run the application"""
    print("🎓 LectoAI - AI Learning Assistant")
    print("=" * 40)
    
    # Check dependencies
    if not check_dependencies():
        return
    
    # Start backend
    backend_process = start_backend()
    if not backend_process:
        return
    
    # Wait a moment for backend to start
    print("⏳ Waiting for backend to start...")
    time.sleep(3)
    
    # Open frontend
    if open_frontend():
        print("\n✅ Application started successfully!")
        print("📚 Backend API: http://localhost:5000")
        print("🌐 Frontend: Opened in your default browser")
        print("\n💡 Instructions:")
        print("1. Upload your lecture files (PDF, DOCX, PPTX, images)")
        print("2. Ask questions about your materials")
        print("3. Get AI-powered explanations and summaries")
        print("\n⚠️  Press Ctrl+C to stop the application")
        
        try:
            # Keep the script running
            backend_process.wait()
        except KeyboardInterrupt:
            print("\n🛑 Stopping application...")
            backend_process.terminate()
            print("✅ Application stopped successfully!")
    else:
        backend_process.terminate()

if __name__ == "__main__":
    main()
