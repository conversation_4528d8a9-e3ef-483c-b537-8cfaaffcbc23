# 🎓 LectoAI - Enhanced AI Learning Assistant

An intelligent lecture notes assistant powered by DeepSeek R1 AI with advanced features for optimal learning experience.

## ✨ Enhanced Features

### 🚀 **Performance & Intelligence**
- ⚡ **Optimized AI Responses**: Enhanced prompt engineering for better, more detailed answers
- 🔄 **Retry Mechanisms**: Automatic retry with exponential backoff for failed requests
- ⏱️ **Smart Timeouts**: Intelligent timeout handling with progress indicators
- 🎯 **Context-Aware AI**: Analyzes content type (CS, Math, Science, etc.) for specialized responses

### 📝 **Advanced Response Rendering**
- 🎨 **Markdown Support**: Full markdown rendering with syntax highlighting
- 💻 **Code Highlighting**: Automatic syntax highlighting for programming languages
- 📊 **Table Rendering**: Beautiful table formatting for structured data
- 🧮 **Math Support**: LaTeX/MathJax rendering for mathematical expressions
- 📋 **Rich Formatting**: Headers, lists, blockquotes, and emphasis

### 🎯 **Dynamic Question Generation**
- 🤖 **AI-Generated Questions**: Creates specific questions based on your document content
- 📚 **Content-Aware Suggestions**: Analyzes document type for relevant question types
- 🎲 **Intelligent Fallbacks**: Smart fallback questions when AI generation fails
- 🔍 **Document Analysis**: Understands context to suggest meaningful questions

### 📄 **File Processing**
- 📄 **Multi-format Support**: PDF, DOCX, PPTX, and image files
- 🔍 **OCR Support**: Extract text from images using Tesseract
- 📊 **Progress Tracking**: Real-time upload and processing progress
- 🛡️ **Error Handling**: Comprehensive error handling with user-friendly messages

### 🎨 **User Experience**
- 🌙 **Modern Dark UI**: Professional, responsive interface with smooth animations
- 📱 **Mobile Friendly**: Fully responsive design for all devices
- 🔄 **Loading States**: Beautiful loading indicators with progress bars
- ⚡ **Smooth Animations**: Enhanced UI transitions and micro-interactions

## 🚀 Quick Start

### Option 1: Simple Run (Recommended)
```bash
python run.py
```

### Option 2: Manual Setup
1. **Install Dependencies**
   ```bash
   pip install -r requirements.txt
   ```

2. **Start Backend**
   ```bash
   cd backend
   python app.py
   ```

3. **Open Frontend**
   - Open `frontend/index.html` in your browser
   - Or visit: `file:///path/to/frontend/index.html`

## 📋 Requirements

- Python 3.7+
- Internet connection (for AI API)
- Tesseract OCR (for image processing)

### Installing Tesseract
- **Windows**: Download from [GitHub releases](https://github.com/UB-Mannheim/tesseract/wiki)
- **macOS**: `brew install tesseract`
- **Linux**: `sudo apt-get install tesseract-ocr`

## 🔧 Configuration

1. Copy `.env.example` to `.env`
2. Update your API key if needed (already configured)
3. Adjust settings as required

## 📁 Project Structure

```
AI Learning Assistant/
├── backend/
│   └── app.py              # Flask backend server
├── frontend/
│   └── index.html          # Web interface
├── requirements.txt        # Python dependencies
├── run.py                 # Simple startup script
├── .env.example           # Environment configuration
└── README.md              # This file
```

## 🎯 Enhanced Usage

### 📤 **File Upload & Processing**
1. **Upload Files**: Drag and drop or click to upload lecture materials (PDF, DOCX, PPTX, images)
2. **Real-time Processing**: Watch progress indicators as your file is processed
3. **Content Preview**: See a preview of extracted text content
4. **Dynamic Questions**: Get AI-generated questions specific to your document

### 💬 **Intelligent Q&A**
1. **Ask Questions**: Type questions about your uploaded content or general topics
2. **Rich Responses**: Receive beautifully formatted responses with:
   - **Markdown formatting** for better readability
   - **Code syntax highlighting** for programming content
   - **Mathematical expressions** rendered with MathJax
   - **Tables and lists** for structured information
3. **Smart Suggestions**: Click on AI-generated question suggestions
4. **Retry Mechanism**: Automatic retry for failed requests with progress feedback

### 🎨 **Interactive Features**
- **Smooth Animations**: Enjoy enhanced UI transitions and loading states
- **Progress Tracking**: Real-time feedback during file processing and AI responses
- **Error Recovery**: Intelligent error handling with retry options
- **Mobile Support**: Full functionality on mobile devices

## 🔑 API Integration

This application uses DeepSeek R1 via OpenRouter with your provided API key:
- **Model**: `deepseek/deepseek-r1`
- **Endpoint**: OpenRouter API
- **Features**: Context-aware responses, educational focus

## 🛠️ Troubleshooting

### Common Issues

1. **Dependencies Missing**
   ```bash
   pip install -r requirements.txt
   ```

2. **Tesseract Not Found**
   - Install Tesseract OCR
   - Add to system PATH
   - Set `TESSERACT_CMD` in `.env` if needed

3. **API Errors**
   - Check internet connection
   - Verify API key is correct
   - Check OpenRouter service status

4. **File Upload Issues**
   - Ensure file size < 10MB
   - Check file format is supported
   - Verify backend server is running

### Enhanced Backend Endpoints

- `GET /` - API information and available endpoints
- `GET /health` - Health check with service status
- `POST /upload` - Enhanced file upload with progress tracking and validation
- `POST /ask` - Intelligent AI responses with markdown formatting and retry support
- `POST /generate-questions` - **NEW**: Dynamic question generation based on document content

### 🆕 **What's New in This Version**

#### 🚀 **Performance Improvements**
- **Faster AI Responses**: Optimized API calls with intelligent retry mechanisms
- **Better Error Handling**: Comprehensive error recovery with user-friendly messages
- **Progress Indicators**: Real-time feedback during file processing and AI generation
- **Smart Timeouts**: Configurable timeouts with automatic retry for failed requests

#### 🎨 **Enhanced UI/UX**
- **Markdown Rendering**: Full markdown support with syntax highlighting
- **Mathematical Expressions**: LaTeX/MathJax support for complex formulas
- **Code Highlighting**: Automatic syntax highlighting for 180+ programming languages
- **Smooth Animations**: Enhanced loading states and micro-interactions
- **Responsive Design**: Improved mobile experience with touch-friendly controls

#### 🤖 **AI Intelligence Upgrades**
- **Context-Aware Responses**: AI analyzes content type for specialized responses
- **Enhanced Prompts**: Better prompt engineering for more detailed, educational answers
- **Dynamic Questions**: AI generates specific questions based on your document content
- **Structured Responses**: Responses formatted with headers, lists, and proper organization

#### 📊 **Content Processing**
- **Better Text Extraction**: Improved processing for PDF, DOCX, PPTX, and images
- **Content Analysis**: AI analyzes document type to provide relevant suggestions
- **Preview Generation**: Smart content previews with key information highlighted
- **Error Recovery**: Graceful handling of processing errors with helpful feedback

## 🎨 Features in Detail

### File Processing
- **PDF**: Text extraction with PyMuPDF
- **DOCX**: Document and table text extraction
- **PPTX**: Slide content extraction
- **Images**: OCR text recognition

### AI Capabilities
- Context-aware responses
- Educational explanations
- Concept clarification
- Content summarization
- Interactive Q&A

## 📝 License

This project is for educational purposes. Please ensure compliance with your institution's academic integrity policies.

## 🤝 Contributing

Feel free to submit issues and enhancement requests!

---

**Happy Learning! 🎓✨**
